# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
org.gradle.jvmargs=-Xmx4608m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 --add-opens java.base/java.io=ALL-UNNAMED

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

android.useAndroidX=true
android.enableJetifier=true

# Version of flipper SDK to use with React Native
FLIPPER_VERSION=0.99.0

MYAPP_RELEASE_STORE_FILE=tierra.keystore
MYAPP_RELEASE_KEY_ALIAS=tierra
MYAPP_RELEASE_STORE_PASSWORD=kJGeKu4Y4EkWkkL3m4zbYBQ
MYAPP_RELEASE_KEY_PASSWORD=kJGeKu4Y4EkWkkL3m4zbYBQ

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true
