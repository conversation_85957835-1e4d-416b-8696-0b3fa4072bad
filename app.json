{"name": "tendlyrn", "displayName": "tendlyrn", "expo": {"name": "Tierra Encantada", "displayName": "Tierra Encantada", "icon": "app/images/override/icon.png", "fonts": [{"asset": "./app/assets/fonts/Nucleo.ttf"}, {"asset": "./node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"}, {"asset": "node_modules/native-base/Fonts/Roboto.ttf"}, {"asset": "node_modules/native-base/Fonts/Roboto_medium.ttf"}, {"asset": "node_modules/native-base/Fonts/Ionicons.ttf"}], "extra": {"eas": {"projectId": ""}, "bugsnag": {"apiKey": "33b2d76d54d686eeedcb6293b7d319ae"}}, "version": "3.0.2", "runtimeVersion": "3.0.1", "updates": {"enabled": true, "fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/", "checkAutomatically": "ON_LOAD"}, "android": {"package": "com.lineleader.tierra", "jsEngine": "hermes", "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.VIBRATE"], "googleServicesFile": "./android/firebase-service.json", "versionCode": 2025042902}, "ios": {"bundleIdentifier": "com.lineleader.tierra", "jsEngine": "jsc", "buildNumber": "2025042902", "supportsTablet": true}, "owner": "<PERSON><PERSON><PERSON>", "plugins": [["config-plugin-react-native-intercom", {"appId": "d1mgbeuy", "androidApiKey": "android_sdk-bf2fdfe1e7598a0ac9e6f7e37e4d1a147dec82f3", "iosApiKey": "ios_sdk-6bd5b9e633394f45c6e3c14aaf962b0858e6fdc5", "intercomRegion": "US"}], ["expo-notifications", {"icon": "./app/images/override/icon.png", "color": "#ffffff"}], ["expo-build-properties", {"ios": {"useFrameworks": "static", "deploymentTarget": "13.4"}}], "expo-secure-store", "@bugsnag/plugin-expo-eas-sourcemaps"], "developmentClient": {"silentLaunch": true}, "slug": "tierra", "scheme": "tierra"}}